import { world, system } from '@minecraft/server';
import { getRandomInt } from '../../utils/rng';
import { getRandomLocation } from '../../utils/vector3';
function getZombieWeightsByDay() {
    const currentDay = world.getDay();
    const weights = [];
    if (currentDay >= 2) {
        weights.push({ type: 'walker', weight: 50 });
    }
    if (currentDay >= 15) {
        weights.push({ type: 'miner', weight: 20 });
    }
    if (currentDay >= 30) {
        weights.push({ type: 'feral', weight: 15 });
    }
    if (currentDay >= 50) {
        weights.push({ type: 'spitter', weight: 15 });
    }
    return weights;
}
export function spawnZombies(location, dimension) {
    const baseOffset = 0;
    const additionalOffset = 3;
    const yOffset = 1;
    const minZombies = 1;
    const maxZombies = 4;
    try {
        const zombieCount = getRandomInt(minZombies, maxZombies);
        const availableZombies = getZombieWeightsByDay();
        if (availableZombies.length === 0) {
            return;
        }
        let spawnDelay = 0;
        for (let i = 0; i < zombieCount; i++) {
            const totalWeight = availableZombies.reduce((sum, { weight }) => sum + weight, 0);
            const randomNum = getRandomInt(0, totalWeight);
            let cumulativeWeight = 0;
            let zombieType = '';
            for (const { type, weight } of availableZombies) {
                cumulativeWeight += weight;
                if (randomNum <= cumulativeWeight) {
                    zombieType = type;
                    break;
                }
            }
            const spawnPos = getRandomLocation(location, dimension, baseOffset, additionalOffset, yOffset, true);
            if (spawnPos) {
                const entityTypeMap = {
                    'walker': 'rza:walker',
                    'miner': 'rza:miner',
                    'feral': 'rza:feral',
                    'spitter': 'rza:spitter'
                };
                const entityType = entityTypeMap[zombieType];
                if (entityType) {
                    system.runTimeout(() => {
                        try {
                            dimension.spawnEntity(entityType, spawnPos);
                        }
                        catch (error) {
                            console.warn(`Error spawning ${entityType}: ${error}`);
                        }
                    }, spawnDelay);
                    spawnDelay += 1;
                }
            }
        }
    }
    catch (error) {
        console.warn(`Error spawning zombies: ${error}`);
        if (error instanceof Error) {
            console.warn(`Stack trace: ${error.stack}`);
        }
    }
}
