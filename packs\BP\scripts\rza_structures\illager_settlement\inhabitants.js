import { system } from '@minecraft/server';
import { getRandomLocation } from '../utils/vector3';
import { getRandomInt, getRandomElement } from '../utils/rng';
export async function summonInhabitants(location, dimension) {
    const entities = [
        {
            entityType: 'minecraft:pillager',
            min: 3,
            max: 8
        },
        {
            entityType: 'minecraft:vindicator',
            min: 2,
            max: 7
        },
        {
            entityType: 'minecraft:evocation_illager',
            min: 1,
            max: 2
        }
    ];
    const baseOffset = 0;
    const additionalOffset = 5;
    const yOffset = 1;
    try {
        let spawnDelay = 0;
        for (const entity of entities) {
            const count = getRandomInt(entity.min, entity.max);
            for (let i = 0; i < count; i++) {
                const spawnPos = getRandomLocation(location, dimension, baseOffset, additionalOffset, yOffset, true);
                if (spawnPos) {
                    system.runTimeout(() => {
                        try {
                            dimension.spawnEntity(entity.entityType, spawnPos);
                        }
                        catch (error) {
                            console.warn(`Error spawning ${entity.entityType}: ${error}`);
                        }
                    }, spawnDelay);
                    spawnDelay += 1;
                }
            }
        }
        for (let i = 0; i < 2; i++) {
            const ravagerSpawnPos = getRandomLocation(location, dimension, baseOffset, additionalOffset, yOffset, true);
            if (ravagerSpawnPos) {
                system.runTimeout(() => {
                    try {
                        dimension.spawnEntity('minecraft:ravager<rza:spawn_for_village>', ravagerSpawnPos);
                    }
                    catch (error) {
                        console.warn(`Error spawning ravager: ${error}`);
                    }
                }, spawnDelay);
                spawnDelay += 1;
            }
        }
    }
    catch (error) {
        console.warn(`Error spawning illager settlement inhabitants: ${error}`);
    }
    return;
}
export function summonVillager(location, dimension) {
    try {
        dimension.spawnEntity('minecraft:villager', location);
    }
    catch (error) {
        console.warn(`Error spawning villager: ${error}`);
    }
    return;
}
export function summonAnimals(location, dimension, minCount, maxCount) {
    try {
        const animalTypes = [
            'minecraft:cow',
            'minecraft:pig',
            'minecraft:sheep',
            'minecraft:chicken',
            'minecraft:horse'
        ];
        const count = getRandomInt(minCount, maxCount);
        let spawnDelay = 0;
        for (let i = 0; i < count; i++) {
            if (location) {
                const selectedAnimal = getRandomElement(animalTypes);
                system.runTimeout(() => {
                    try {
                        dimension.spawnEntity(selectedAnimal, location);
                    }
                    catch (error) {
                        console.warn(`Error spawning ${selectedAnimal}: ${error}`);
                    }
                }, spawnDelay);
                spawnDelay += 1;
            }
        }
    }
    catch (error) {
        console.warn(`Error spawning small animals: ${error}`);
    }
    return;
}
export function summonSheep(location, dimension) {
    try {
        const count = getRandomInt(2, 4);
        let spawnDelay = 0;
        for (let i = 0; i < count; i++) {
            if (location) {
                const roll = Math.random();
                const animalType = roll < 0.25 ? 'minecraft:goat' : 'minecraft:sheep';
                system.runTimeout(() => {
                    try {
                        dimension.spawnEntity(animalType, location);
                    }
                    catch (error) {
                        console.warn(`Error spawning ${animalType}: ${error}`);
                    }
                }, spawnDelay);
                spawnDelay += 1;
            }
        }
    }
    catch (error) {
        console.warn(`Error spawning sheep/goat: ${error}`);
    }
    return;
}
export function summonButcherAnimals(location, dimension, minCount, maxCount) {
    try {
        const animalTypes = [
            'minecraft:cow',
            'minecraft:pig',
            'minecraft:sheep',
            'minecraft:chicken'
        ];
        const count = getRandomInt(minCount, maxCount);
        let spawnDelay = 0;
        for (let i = 0; i < count; i++) {
            if (location) {
                const selectedAnimal = getRandomElement(animalTypes);
                system.runTimeout(() => {
                    try {
                        dimension.spawnEntity(selectedAnimal, location);
                    }
                    catch (error) {
                        console.warn(`Error spawning ${selectedAnimal}: ${error}`);
                    }
                }, spawnDelay);
                spawnDelay += 1;
            }
        }
    }
    catch (error) {
        console.warn(`Error spawning butcher animals: ${error}`);
    }
    return;
}
