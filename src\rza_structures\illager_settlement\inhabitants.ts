import { Vector3, Dimension, system } from '@minecraft/server';
import { getRandomLocation } from '../utils/vector3';
import { getRandomInt, getRandomElement } from '../utils/rng';

/**
 * Summons random inhabitant illagers for an illager settlement
 * @param location The center location to spawn entities around
 * @param dimension The dimension to spawn entities in
 */
export async function summonInhabitants(location: Vector3, dimension: Dimension): Promise<void> {
    // Define entity types and their spawn ranges
    const entities = [
        {
            entityType: 'minecraft:pillager',
            min: 3,
            max: 8
        },
        {
            entityType: 'minecraft:vindicator',
            min: 2,
            max: 7
        },
        {
            entityType: 'minecraft:evocation_illager',
            min: 1,
            max: 2
        }
    ];

    // Spawn settings
    const baseOffset = 0;
    const additionalOffset = 5;
    const yOffset = 1;

    try {
        let spawnDelay = 0;

        // Spawn each type of entity with 1-tick delays
        for (const entity of entities) {
            const count = getRandomInt(entity.min, entity.max);

            for (let i = 0; i < count; i++) {
                const spawnPos = getRandomLocation(
                    location,
                    dimension,
                    baseOffset,
                    additionalOffset,
                    yOffset,
                    true
                );

                if (spawnPos) {
                    system.runTimeout(() => {
                        try {
                            dimension.spawnEntity(entity.entityType as any, spawnPos);
                        } catch (error) {
                            console.warn(`Error spawning ${entity.entityType}: ${error}`);
                        }
                    }, spawnDelay);
                    spawnDelay += 1; // Increment delay by 1 tick
                }
            }
        }

        // Spawn the ravagers separately with the special event
        for (let i = 0; i < 2; i++) {
            const ravagerSpawnPos = getRandomLocation(
                location,
                dimension,
                baseOffset,
                additionalOffset,
                yOffset,
                true
            );

            if (ravagerSpawnPos) {
                system.runTimeout(() => {
                    try {
                        dimension.spawnEntity('minecraft:ravager<rza:spawn_for_village>' as any, ravagerSpawnPos);
                    } catch (error) {
                        console.warn(`Error spawning ravager: ${error}`);
                    }
                }, spawnDelay);
                spawnDelay += 1; // Increment delay by 1 tick
            }
        }
    } catch (error) {
        console.warn(`Error spawning illager settlement inhabitants: ${error}`);
    }

    return;
}

/**
 * Summons a single villager at the specified location
 * @param location The exact location to spawn the villager
 * @param dimension The dimension to spawn the villager in
 */
export function summonVillager(location: Vector3, dimension: Dimension): void {
    try {
        dimension.spawnEntity('minecraft:villager' as any, location);
    } catch (error) {
        console.warn(`Error spawning villager: ${error}`);
    }

    return;
}

/**
 * Summons 2-3 random farm animals at the specified location
 * @param location The center location to spawn animals around
 * @param dimension The dimension to spawn the animals in
 */
export function summonAnimals(
    location: Vector3,
    dimension: Dimension,
    minCount: number,
    maxCount: number
): void {
    try {
        const animalTypes = [
            'minecraft:cow',
            'minecraft:pig',
            'minecraft:sheep',
            'minecraft:chicken',
            'minecraft:horse'
        ];

        const count = getRandomInt(minCount, maxCount);
        let spawnDelay = 0;

        for (let i = 0; i < count; i++) {
            if (location) {
                const selectedAnimal = getRandomElement(animalTypes);
                system.runTimeout(() => {
                    try {
                        dimension.spawnEntity(selectedAnimal as any, location);
                    } catch (error) {
                        console.warn(`Error spawning ${selectedAnimal}: ${error}`);
                    }
                }, spawnDelay);
                spawnDelay += 1; // Increment delay by 1 tick
            }
        }
    } catch (error) {
        console.warn(`Error spawning small animals: ${error}`);
    }

    return;
}

/**
 * Summons 2-4 sheep at the specified location with a 25% chance to summon a goat
 * @param location The exact location to spawn the sheep
 * @param dimension The dimension to spawn the sheep in
 */
export function summonSheep(location: Vector3, dimension: Dimension): void {
    try {
        const count = getRandomInt(2, 4);
        let spawnDelay = 0;

        for (let i = 0; i < count; i++) {
            if (location) {
                // 25% chance to spawn a goat instead of a sheep
                const roll = Math.random();
                const animalType = roll < 0.25 ? 'minecraft:goat' : 'minecraft:sheep';

                system.runTimeout(() => {
                    try {
                        dimension.spawnEntity(animalType as any, location);
                    } catch (error) {
                        console.warn(`Error spawning ${animalType}: ${error}`);
                    }
                }, spawnDelay);
                spawnDelay += 1; // Increment delay by 1 tick
            }
        }
    } catch (error) {
        console.warn(`Error spawning sheep/goat: ${error}`);
    }

    return;
}

/**
 * Summons butcher-related farm animals (cows, pigs, sheep, chickens) at the specified location
 * @param location The center location to spawn animals around
 * @param dimension The dimension to spawn the animals in
 * @param minCount Minimum number of animals to spawn
 * @param maxCount Maximum number of animals to spawn
 */
export function summonButcherAnimals(
    location: Vector3,
    dimension: Dimension,
    minCount: number,
    maxCount: number
): void {
    try {
        const animalTypes = [
            'minecraft:cow',
            'minecraft:pig',
            'minecraft:sheep',
            'minecraft:chicken'
        ];

        const count = getRandomInt(minCount, maxCount);
        let spawnDelay = 0;

        for (let i = 0; i < count; i++) {
            if (location) {
                const selectedAnimal = getRandomElement(animalTypes);
                system.runTimeout(() => {
                    try {
                        dimension.spawnEntity(selectedAnimal as any, location);
                    } catch (error) {
                        console.warn(`Error spawning ${selectedAnimal}: ${error}`);
                    }
                }, spawnDelay);
                spawnDelay += 1; // Increment delay by 1 tick
            }
        }
    } catch (error) {
        console.warn(`Error spawning butcher animals: ${error}`);
    }

    return;
}
